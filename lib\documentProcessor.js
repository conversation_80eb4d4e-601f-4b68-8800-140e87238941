// lib/documentProcessor.js
import fs from 'fs';
import path from 'path';
import pdfParse from 'pdf-parse';
import mammoth from 'mammoth';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { extractDatesAndEvents } from './eventExtractor.js';
import { storeEvents } from './eventsDatabase.js';

/**
 * Extract text from different file types
 */
export async function extractTextFromFile(filePath, originalFilename) {
  try {
    const fileExtension = path.extname(originalFilename).toLowerCase();
    const fileBuffer = fs.readFileSync(filePath);

    let extractedText = '';

    switch (fileExtension) {
      case '.pdf':
        console.log('📄 Processing PDF file...');
        const pdfData = await pdfParse(fileBuffer);
        extractedText = pdfData.text;
        break;

      case '.docx':
        console.log('📝 Processing DOCX file...');
        const docxResult = await mammoth.extractRawText({ buffer: fileBuffer });
        extractedText = docxResult.value;
        break;

      case '.txt':
        console.log('📃 Processing TXT file...');
        extractedText = fileBuffer.toString('utf-8');
        break;

      default:
        throw new Error(`Unsupported file type: ${fileExtension}`);
    }

    if (!extractedText || extractedText.trim().length === 0) {
      throw new Error('No text could be extracted from the file');
    }

    console.log(`✅ Extracted ${extractedText.length} characters from ${originalFilename}`);
    return extractedText;

  } catch (error) {
    console.error(`❌ Error extracting text from ${originalFilename}:`, error);
    throw error;
  }
}

/**
 * Split text into chunks for embedding
 */
export async function chunkText(text, options = {}) {
  try {
    const {
      chunkSize = 1000,
      chunkOverlap = 200,
      separators = ['\n\n', '\n', '. ', ' ', '']
    } = options;

    console.log('✂️ Splitting text into chunks...');

    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize,
      chunkOverlap,
      separators
    });

    const chunks = await textSplitter.splitText(text);

    console.log(`✅ Created ${chunks.length} chunks (avg ${Math.round(text.length / chunks.length)} chars each)`);

    return chunks.map((chunk, index) => ({
      id: index,
      text: chunk.trim(),
      length: chunk.length
    }));

  } catch (error) {
    console.error('❌ Error chunking text:', error);
    throw error;
  }
}

/**
 * Process a single uploaded file
 */
export async function processUploadedFile(filePath, originalFilename, metadata = {}) {
  try {
    console.log(`🔄 Processing file: ${originalFilename}`);

    // Extract text from file
    const extractedText = await extractTextFromFile(filePath, originalFilename);

    // Chunk the text
    const chunks = await chunkText(extractedText);

    // Add metadata to each chunk
    const processedChunks = chunks.map(chunk => ({
      ...chunk,
      metadata: {
        filename: originalFilename,
        fileType: path.extname(originalFilename).toLowerCase(),
        uploadDate: new Date().toISOString(),
        chunkIndex: chunk.id,
        totalChunks: chunks.length,
        ...metadata
      }
    }));

    console.log(`✅ Successfully processed ${originalFilename} into ${chunks.length} chunks`);

    // Extract events from the document (Smart Academic Event Calendar)
    let extractedEvents = [];
    let eventExtractionResult = null;

    try {
      console.log(`🗓️ Extracting academic events from ${originalFilename}...`);
      extractedEvents = extractDatesAndEvents(extractedText, originalFilename);

      if (extractedEvents.length > 0) {
        eventExtractionResult = await storeEvents(extractedEvents);
        console.log(`📅 Event extraction completed: ${extractedEvents.length} events found`);
      } else {
        console.log(`📅 No academic events found in ${originalFilename}`);
      }
    } catch (eventError) {
      console.error(`⚠️ Event extraction failed for ${originalFilename}:`, eventError);
      // Don't fail the entire process if event extraction fails
    }

    return {
      filename: originalFilename,
      totalChunks: chunks.length,
      totalCharacters: extractedText.length,
      chunks: processedChunks,
      events: {
        extracted: extractedEvents.length,
        stored: eventExtractionResult?.stored || 0,
        details: extractedEvents
      }
    };

  } catch (error) {
    console.error(`❌ Error processing file ${originalFilename}:`, error);
    throw error;
  }
}

/**
 * Validate file type and size
 */
export function validateFile(file, options = {}) {
  const {
    maxSizeBytes = 10 * 1024 * 1024, // 10MB default
    allowedExtensions = ['.pdf', '.docx', '.txt']
  } = options;

  const fileExtension = path.extname(file.originalFilename || file.name).toLowerCase();

  // Check file type
  if (!allowedExtensions.includes(fileExtension)) {
    throw new Error(`File type ${fileExtension} is not supported. Allowed types: ${allowedExtensions.join(', ')}`);
  }

  // Check file size
  if (file.size > maxSizeBytes) {
    throw new Error(`File size ${Math.round(file.size / 1024 / 1024)}MB exceeds maximum allowed size of ${Math.round(maxSizeBytes / 1024 / 1024)}MB`);
  }

  return true;
}

export default {
  extractTextFromFile,
  chunkText,
  processUploadedFile,
  validateFile
};
