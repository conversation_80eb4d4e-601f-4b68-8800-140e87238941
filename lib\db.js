// lib/db.js
import sqlite3 from 'sqlite3';
import { open } from 'sqlite';
import path from 'path';

let db = null;

async function getDatabase() {
  if (!db) {
    const dbPath = path.join(process.cwd(), 'database', 'vtu_gpt.db');

    db = await open({
      filename: dbPath,
      driver: sqlite3.Database
    });
  }
  return db;
}

// Create a pool-like interface to maintain compatibility
const pool = {
  async query(sql, params = []) {
    const database = await getDatabase();

    // Handle different query types
    if (sql.trim().toUpperCase().startsWith('SELECT')) {
      const rows = await database.all(sql, params);
      return { rows };
    } else if (sql.trim().toUpperCase().startsWith('INSERT') && sql.includes('RETURNING')) {
      // Handle INSERT with RETURNING clause
      const insertSql = sql.replace(/RETURNING.*$/i, '');
      const result = await database.run(insertSql, params);

      // Get the inserted row
      const selectSql = 'SELECT * FROM users WHERE id = ?';
      const rows = await database.all(selectSql, [result.lastID]);
      return { rows };
    } else {
      // Handle other queries (INSERT, UPDATE, DELETE)
      const result = await database.run(sql, params);
      return {
        rows: [],
        rowCount: result.changes,
        lastID: result.lastID
      };
    }
  },

  async end() {
    if (db) {
      await db.close();
      db = null;
    }
  }
};

export default pool;
export { getDatabase };
