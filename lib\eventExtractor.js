// lib/eventExtractor.js
/**
 * Smart Academic Event Calendar - Event Extraction Module
 * Extracts dates and events from academic documents using NLP and regex patterns
 */

/**
 * Extract dates and events from text content
 * @param {string} text - The text content to analyze
 * @param {string} sourceFile - The source file name
 * @returns {Array} Array of extracted events
 */
export function extractDatesAndEvents(text, sourceFile) {
  try {
    console.log(`🔍 Extracting events from ${sourceFile}...`);
    
    const events = [];
    const lines = text.split('\n');
    
    // Academic event keywords and their types
    const eventKeywords = {
      'exam': ['exam', 'examination', 'test', 'assessment', 'evaluation'],
      'registration': ['registration', 'register', 'enroll', 'enrollment', 'admission'],
      'deadline': ['deadline', 'last date', 'due date', 'final date', 'closing date'],
      'fee': ['fee', 'payment', 'tuition', 'charges', 'dues'],
      'holiday': ['holiday', 'vacation', 'break', 'closed', 'off'],
      'workshop': ['workshop', 'seminar', 'conference', 'training', 'session'],
      'result': ['result', 'results', 'marks', 'grades', 'score'],
      'revaluation': ['revaluation', 'recheck', 'review', 'appeal'],
      'internship': ['internship', 'placement', 'job', 'career', 'recruitment'],
      'project': ['project', 'thesis', 'dissertation', 'submission']
    };
    
    // Date patterns (various formats)
    const datePatterns = [
      // DD/MM/YYYY, DD-MM-YYYY, DD.MM.YYYY
      /\b(\d{1,2})[\/\-\.](\d{1,2})[\/\-\.](\d{4})\b/g,
      // DD Month YYYY, DD Month YY
      /\b(\d{1,2})\s+(January|February|March|April|May|June|July|August|September|October|November|December|Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+(\d{2,4})\b/gi,
      // Month DD, YYYY
      /\b(January|February|March|April|May|June|July|August|September|October|November|December|Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+(\d{1,2}),?\s+(\d{4})\b/gi,
      // DD-Month-YYYY
      /\b(\d{1,2})-(January|February|March|April|May|June|July|August|September|October|November|December|Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d{4})\b/gi
    ];
    
    // Process each line for event detection
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line.length < 10) continue; // Skip very short lines
      
      // Check for event keywords
      const detectedEventType = detectEventType(line, eventKeywords);
      if (!detectedEventType) continue;
      
      // Extract dates from current line and surrounding lines
      const contextLines = [
        lines[i - 1] || '',
        line,
        lines[i + 1] || ''
      ].join(' ');
      
      const extractedDates = extractDatesFromText(contextLines, datePatterns);
      
      // Create events for each detected date
      extractedDates.forEach(dateInfo => {
        const event = {
          title: cleanEventTitle(line),
          date: dateInfo.standardDate,
          sourceFile: sourceFile,
          eventType: detectedEventType,
          description: line.length > 100 ? line.substring(0, 100) + '...' : line,
          confidence: calculateConfidence(line, detectedEventType, dateInfo)
        };
        
        // Only add high-confidence events
        if (event.confidence > 0.6) {
          events.push(event);
        }
      });
    }
    
    // Remove duplicates and sort by date
    const uniqueEvents = removeDuplicateEvents(events);
    const sortedEvents = uniqueEvents.sort((a, b) => new Date(a.date) - new Date(b.date));
    
    console.log(`✅ Extracted ${sortedEvents.length} events from ${sourceFile}`);
    return sortedEvents;
    
  } catch (error) {
    console.error(`❌ Error extracting events from ${sourceFile}:`, error);
    return [];
  }
}

/**
 * Detect event type based on keywords
 */
function detectEventType(text, eventKeywords) {
  const lowerText = text.toLowerCase();
  
  for (const [type, keywords] of Object.entries(eventKeywords)) {
    if (keywords.some(keyword => lowerText.includes(keyword))) {
      return type;
    }
  }
  
  return null;
}

/**
 * Extract dates from text using multiple patterns
 */
function extractDatesFromText(text, datePatterns) {
  const dates = [];
  
  datePatterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(text)) !== null) {
      const standardDate = standardizeDate(match);
      if (standardDate && isValidAcademicDate(standardDate)) {
        dates.push({
          originalText: match[0],
          standardDate: standardDate,
          position: match.index
        });
      }
    }
  });
  
  return dates;
}

/**
 * Standardize date to YYYY-MM-DD format
 */
function standardizeDate(match) {
  try {
    const monthNames = {
      'january': '01', 'jan': '01',
      'february': '02', 'feb': '02',
      'march': '03', 'mar': '03',
      'april': '04', 'apr': '04',
      'may': '05',
      'june': '06', 'jun': '06',
      'july': '07', 'jul': '07',
      'august': '08', 'aug': '08',
      'september': '09', 'sep': '09',
      'october': '10', 'oct': '10',
      'november': '11', 'nov': '11',
      'december': '12', 'dec': '12'
    };
    
    let day, month, year;
    
    // Handle different match patterns
    if (match[1] && match[2] && match[3]) {
      if (isNaN(match[2])) {
        // Month name format
        day = match[1].padStart(2, '0');
        month = monthNames[match[2].toLowerCase()];
        year = match[3].length === 2 ? '20' + match[3] : match[3];
      } else {
        // Numeric format
        day = match[1].padStart(2, '0');
        month = match[2].padStart(2, '0');
        year = match[3];
      }
    }
    
    if (!day || !month || !year) return null;
    
    const date = new Date(year, month - 1, day);
    if (isNaN(date.getTime())) return null;
    
    return `${year}-${month}-${day}`;
    
  } catch (error) {
    return null;
  }
}

/**
 * Check if date is within reasonable academic timeframe
 */
function isValidAcademicDate(dateString) {
  const date = new Date(dateString);
  const now = new Date();
  const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
  const twoYearsFromNow = new Date(now.getFullYear() + 2, now.getMonth(), now.getDate());
  
  return date >= oneYearAgo && date <= twoYearsFromNow;
}

/**
 * Clean and format event title
 */
function cleanEventTitle(text) {
  // Remove excessive whitespace and special characters
  let title = text.replace(/\s+/g, ' ').trim();
  
  // Limit length
  if (title.length > 200) {
    title = title.substring(0, 200) + '...';
  }
  
  // Capitalize first letter
  title = title.charAt(0).toUpperCase() + title.slice(1);
  
  return title;
}

/**
 * Calculate confidence score for extracted event
 */
function calculateConfidence(text, eventType, dateInfo) {
  let confidence = 0.5; // Base confidence
  
  // Boost confidence based on event type keywords
  const eventTypeKeywords = {
    'exam': 0.9,
    'deadline': 0.9,
    'registration': 0.8,
    'fee': 0.7,
    'result': 0.8,
    'revaluation': 0.8
  };
  
  confidence += (eventTypeKeywords[eventType] || 0.6) * 0.3;
  
  // Boost confidence if date is close to text
  if (dateInfo.position < 100) {
    confidence += 0.1;
  }
  
  // Boost confidence for specific phrases
  const highConfidencePhrases = [
    'last date', 'deadline', 'due date', 'final date',
    'exam schedule', 'registration', 'fee payment'
  ];
  
  if (highConfidencePhrases.some(phrase => text.toLowerCase().includes(phrase))) {
    confidence += 0.2;
  }
  
  return Math.min(confidence, 1.0);
}

/**
 * Remove duplicate events
 */
function removeDuplicateEvents(events) {
  const seen = new Set();
  return events.filter(event => {
    const key = `${event.title}-${event.date}-${event.eventType}`;
    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });
}

export default {
  extractDatesAndEvents
};
