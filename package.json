{"name": "vtu-gpt", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "setup-db": "node scripts/setup-db.js", "test-setup": "node scripts/test-setup.js", "manage-users": "node scripts/manage-users.js"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@pinecone-database/pinecone": "^3.0.3", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/nodemailer": "^6.4.17", "axios": "^1.7.9", "bcryptjs": "^3.0.2", "dotenv": "^16.5.0", "firebase": "^11.1.0", "formidable": "^3.5.2", "framer-motion": "^12.4.2", "googleapis": "^144.0.0", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.27", "lucide-react": "^0.475.0", "mammoth": "^1.9.1", "multer": "^1.4.5-lts.1", "next": "^15.2.4", "next-connect": "^1.0.0", "nodemailer": "^7.0.3", "openai": "^4.103.0", "pdf-parse": "^1.1.1", "react": "^19.0.0", "react-calendar": "^5.1.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-icons": "^5.5.0", "readline": "^1.3.0", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "vtu-gpt": "file:"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}