'use client';

import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import chatSyncService from '../services/chatSyncService';
import ClientOnly from './ClientOnly';
import EventCalendar from '../../components/EventCalendar';


const Chatbot = () => {
  const [userQuery, setUserQuery] = useState('');
  const [chatHistory, setChatHistory] = useState([]);
  const [recentChats, setRecentChats] = useState([]);
  const [currentChatId, setCurrentChatId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showLeftPanel, setShowLeftPanel] = useState(false);
  const [showSplash, setShowSplash] = useState(true); // State for splash screen
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncStatus, setSyncStatus] = useState('idle'); // 'idle', 'syncing', 'success', 'error'
  const [isClient, setIsClient] = useState(false); // Track client-side mounting
  const [showCalendar, setShowCalendar] = useState(false); // Smart Academic Event Calendar
  const leftPanelRef = useRef(null); // Reference for the left panel
  const toggleButtonRef = useRef(null); // Reference for the toggle button
  const userMenuRef = useRef(null); // Reference for the user menu

  const { user, logout } = useAuth();

  // Close the left panel and user menu when clicking outside
  useEffect(() => {
    const handleOutsideClick = (event) => {
      if (
        showLeftPanel &&
        leftPanelRef.current &&
        toggleButtonRef.current &&
        !leftPanelRef.current.contains(event.target) &&
        !toggleButtonRef.current.contains(event.target)
      ) {
        setShowLeftPanel(false);
      }

      if (
        showUserMenu &&
        userMenuRef.current &&
        !userMenuRef.current.contains(event.target)
      ) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('click', handleOutsideClick);
    return () => {
      document.removeEventListener('click', handleOutsideClick);
    };
  }, [showLeftPanel, showUserMenu]);

  // Migrate old chat data to new email-based storage
  const migrateOldChatData = (userEmail) => {
    try {
      // Check for old storage format (user ID based)
      const oldKeys = Object.keys(localStorage).filter(key =>
        key.startsWith('vtu-gpt-chats-') && !key.includes('@')
      );

      if (oldKeys.length > 0) {
        const newUserKey = `vtu-gpt-chats-${userEmail}`;
        const existingData = localStorage.getItem(newUserKey);

        if (!existingData) {
          // Migrate the most recent old data
          const oldKey = oldKeys[0];
          const oldData = localStorage.getItem(oldKey);
          if (oldData) {
            localStorage.setItem(newUserKey, oldData);
            console.log('Migrated chat data to new email-based storage');
          }
        }

        // Clean up old storage keys
        oldKeys.forEach(key => localStorage.removeItem(key));
      }
    } catch (error) {
      console.error('Error migrating old chat data:', {
        message: error.message,
        name: error.name
      });
    }
  };



  // Client-side mounting effect
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Load recent chats using new API-based sync service
  useEffect(() => {
    const loadRecentChats = async () => {
      try {
        if (!user?.email || !isClient) return;

        setIsSyncing(true);
        setSyncStatus('syncing');

        // Migrate old data if needed (only on client)
        if (typeof window !== 'undefined') {
          migrateOldChatData(user.email);
        }

        // Try to fetch from server first
        try {
          const serverChats = await chatSyncService.fetchChats(user.email);
          if (serverChats && serverChats.length > 0) {
            const validChats = validateAndCleanChats(serverChats);
            setRecentChats(validChats);
            setSyncStatus('success');
            console.log('Loaded chats from server');
          } else {
            // Fallback to localStorage
            const localChats = chatSyncService.getLocalChats(user.email);
            const validChats = validateAndCleanChats(localChats);
            setRecentChats(validChats);
            setSyncStatus('success');
            console.log('Loaded chats from local storage');
          }
        } catch (error) {
          console.error('Server fetch failed, using local storage:', error);
          // Fallback to localStorage
          const localChats = chatSyncService.getLocalChats(user.email);
          const validChats = validateAndCleanChats(localChats);
          setRecentChats(validChats);
          setSyncStatus('error');
        }

      } catch (error) {
        console.error('Error loading recent chats:', {
          message: error.message,
          name: error.name
        });
        setSyncStatus('error');
      } finally {
        setTimeout(() => {
          setIsSyncing(false);
          setSyncStatus('idle');
        }, 1000);
      }
    };

    if (user?.email && isClient) {
      loadRecentChats();
      // Note: Automatic periodic sync disabled - only manual sync available
    }

    return () => {
      // Cleanup if needed
      if (isClient) {
        chatSyncService.stopPeriodicSync();
      }
    };
  }, [user?.email, isClient]);

  // Validate and clean chat data
  const validateAndCleanChats = (chats) => {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    return chats.filter(chat => {
      const chatDate = new Date(chat.timestamp);
      return chatDate > thirtyDaysAgo && chat.messages && chat.messages.length > 0;
    });
  };

  // Manual sync function - now the only way to sync chats
  const manualSync = async () => {
    if (!user?.email) return;

    setIsSyncing(true);
    setSyncStatus('syncing');

    try {
      console.log('Starting manual sync...');

      // Force sync from server
      const serverChats = await chatSyncService.manualSync(user.email);

      if (serverChats && serverChats.length >= 0) {
        const validChats = validateAndCleanChats(serverChats);

        // Check if there are any changes
        const currentChatsString = JSON.stringify(recentChats);
        const serverChatsString = JSON.stringify(validChats);

        if (currentChatsString !== serverChatsString) {
          setRecentChats(validChats);
          console.log(`Manual sync completed - ${validChats.length} chats synced from server`);
        } else {
          console.log('Manual sync completed - no changes detected');
        }

        setSyncStatus('success');
      } else {
        // Fallback to local storage
        const localChats = chatSyncService.getLocalChats(user.email);
        const validChats = validateAndCleanChats(localChats);
        setRecentChats(validChats);
        setSyncStatus('success');
        console.log('Manual sync completed from local storage');
      }

    } catch (error) {
      // Safe error logging to prevent circular structure errors
      console.error('Manual sync failed:', {
        message: error.message,
        name: error.name,
        stack: error.stack
      });
      setSyncStatus('error');

      // Try local storage as last resort
      try {
        const localChats = chatSyncService.getLocalChats(user.email);
        const validChats = validateAndCleanChats(localChats);
        setRecentChats(validChats);
        console.log('Fallback to local storage successful');
      } catch (localError) {
        console.error('Local storage fallback also failed:', {
          message: localError.message,
          name: localError.name
        });
      }
    } finally {
      setTimeout(() => {
        setIsSyncing(false);
        setSyncStatus('idle');
      }, 2000);
    }
  };

  useEffect(() => {
    // Add chatbot-page class to body for specific styling
    document.body.classList.add('chatbot-page');

    // Simulate initialization process
    const initializeApp = async () => {
      setIsInitializing(true);

      // Simulate loading time for smooth transition
      await new Promise(resolve => setTimeout(resolve, 1500));

      setIsInitializing(false);

      // Show splash screen briefly after initialization
      setTimeout(() => {
        setShowSplash(false);
      }, 1000);
    };

    initializeApp();

    // Cleanup: remove class when component unmounts
    return () => {
      document.body.classList.remove('chatbot-page');
    };
  }, []);

  // Generate a concise chat title
  const generateChatTitle = (firstMessage) => {
    if (!firstMessage) return 'New Chat';

    // Remove common question words and clean up
    let title = firstMessage
      .replace(/^(what|how|when|where|why|who|tell me|can you|please|could you)\s+/i, '')
      .replace(/\?+$/, '')
      .trim();

    // Limit to 25 characters for better display
    if (title.length > 25) {
      title = title.substring(0, 22) + '...';
    }

    // Capitalize first letter
    return title.charAt(0).toUpperCase() + title.slice(1);
  };

  // Save current chat using new API-based sync service
  const saveCurrentChat = async () => {
    if (chatHistory.length > 0 && user?.email) {
      try {
        const chatId = currentChatId || Date.now().toString();
        const firstUserMessage = chatHistory.find(msg => msg.sender === 'user')?.message;
        const chatData = {
          id: chatId,
          title: generateChatTitle(firstUserMessage),
          messages: chatHistory,
          timestamp: new Date().toISOString(),
          userEmail: user.email,
          deviceId: chatSyncService.getDeviceId(),
          lastModified: Date.now(),
        };

        const updatedChats = recentChats.filter(chat => chat.id !== chatId);
        updatedChats.unshift(chatData);

        // Keep only last 15 chats
        const limitedChats = updatedChats.slice(0, 15);

        setRecentChats(limitedChats);
        setCurrentChatId(chatId);

        // Save to server with fallback to local storage
        setIsSyncing(true);
        const success = await chatSyncService.saveChats(user.email, limitedChats);

        if (success) {
          console.log('Chat saved to server successfully');
        } else {
          console.log('Chat saved to local storage as fallback');
        }

        setTimeout(() => setIsSyncing(false), 500);

      } catch (error) {
        console.error('Error saving chat:', {
          message: error.message,
          name: error.name
        });
      }
    }
  };





  // Load a recent chat
  const loadChat = (chatData) => {
    saveCurrentChat(); // Save current chat before loading new one
    setChatHistory(chatData.messages);
    setCurrentChatId(chatData.id);
    setShowLeftPanel(false);
  };

  // Delete a recent chat using new API-based sync service
  const deleteChat = async (chatId) => {
    if (!user?.email) return;

    try {
      setIsSyncing(true);

      // Try to delete from server first
      const serverSuccess = await chatSyncService.deleteChat(user.email, chatId);

      // Update local state regardless of server success
      const updatedChats = recentChats.filter(chat => chat.id !== chatId);
      setRecentChats(updatedChats);

      // Save updated list to server
      if (!serverSuccess) {
        await chatSyncService.saveChats(user.email, updatedChats);
      }

      if (currentChatId === chatId) {
        handleNewChat();
      }

      setTimeout(() => setIsSyncing(false), 500);
    } catch (error) {
      console.error('Error deleting chat:', {
        message: error.message,
        name: error.name
      });
      setIsSyncing(false);
    }
  };

  const handleNewChat = () => {
    saveCurrentChat(); // Save current chat before starting new one
    setChatHistory([]);
    setUserQuery('');
    setError(null);
    setCurrentChatId(null);
  };

  const handleInputChange = (e) => {
    setUserQuery(e.target.value);
  };

  const handleToggle = () => {
    setShowLeftPanel((prev) => !prev);
  };

  const handleLogout = async () => {
    await logout();
    setShowUserMenu(false);
  };

  const handleSendMessage = async (query) => {
    const message = query || userQuery; // Use query if provided, else use userQuery
    if (!message) return;

    // Ensure message is always a string
    const messageString = typeof message === 'string' ? message : String(message);

    setChatHistory((prev) => [...prev, { sender: 'user', message: messageString }]);
    setIsLoading(true);
    setUserQuery('');

    try {
      const response = await axios.post('/api/chat', { query: messageString }); // Use converted string
      const chatbotResponse = response.data.reply;

      // Ensure the response is a string
      const responseMessage = typeof chatbotResponse === 'string'
        ? chatbotResponse
        : 'Sorry, I received an invalid response. Please try again.';

      setChatHistory((prev) => [
        ...prev,
        { sender: 'chatbot', message: responseMessage },
      ]);
    } catch (err) {
      // Enhanced error logging with fallbacks
      const errorInfo = {
        message: err?.message || 'Unknown error',
        name: err?.name || 'Error',
        status: err?.response?.status || err?.status || 'No status',
        statusText: err?.response?.statusText || err?.statusText || 'No status text',
        data: err?.response?.data || err?.data || 'No response data',
        code: err?.code || 'No error code',
        type: typeof err,
        hasResponse: !!err?.response,
        isAxiosError: err?.isAxiosError || false
      };

      console.error('Chat API error:', errorInfo);
      console.error('Full error object keys:', Object.keys(err || {}));

      setError('Sorry, the chatbot is unavailable. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ClientOnly
      fallback={
        <div className="splash-screen">
          <img src="/images/login-background.png" alt="VTU Logo" className="splash-logo" />
          <div className="splash-content">
            <div className="spinner">
              <i className="fas fa-spinner"></i>
            </div>
            <div className="loading-text">Initializing VTU GPT...</div>
          </div>
        </div>
      }
    >
      <div>
        {/* Animate Presence for Smooth Splash Transition */}
        <AnimatePresence>
          {(showSplash || isInitializing || !isClient) && (
            <motion.div
              className="splash-screen"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 1.2 }}
              transition={{ duration: 0.8 }}
            >
              <img src="/images/login-background.png" alt="VTU Logo" className="splash-logo" />
              <div className="splash-content">
                {isInitializing || !isClient ? (
                  <>
                    <div className="spinner">
                      <i className="fas fa-spinner"></i>
                    </div>
                    <div className="loading-text">Initializing VTU GPT...</div>
                  </>
                ) : (
                  <div className="welcome-text">Welcome to VTU GPT</div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {!showSplash && isClient && (
          <>
      {/* Overlay */}
      {showLeftPanel && <div className="overlay" onClick={() => setShowLeftPanel(false)}></div>}

      <div className="chatbot-container">
        {/* Header */}
        <header className="header">
          <div className="header-title">VTU GPT</div>
          <div className="header-actions">
            {/* Smart Academic Event Calendar Button */}
            <button
              className="calendar-button"
              onClick={() => setShowCalendar(true)}
              title="Academic Event Calendar"
            >
              <i className="fas fa-calendar-alt"></i>
            </button>
          </div>
          <div className="user-section" ref={userMenuRef}>
            <div
              className="user-info"
              onClick={() => setShowUserMenu(!showUserMenu)}
              title={user?.email}
            >
              <span className="user-email">{user?.email}</span>
              <i className="fa fa-user-circle user-icon"></i>
              <i className={`fa fa-chevron-${showUserMenu ? 'up' : 'down'} dropdown-icon`}></i>
            </div>
            {showUserMenu && (
              <div className="user-menu">
                <div className="user-menu-item user-email-display">
                  <i className="fa fa-envelope"></i>
                  <span>{user?.email}</span>
                </div>
                <div className="user-menu-divider"></div>
                <div className="user-menu-item" onClick={() => window.location.href = '/loginpage'}>
                  <i className="fa fa-user-shield"></i>
                  <span>Admin Login</span>
                </div>
                <div className="user-menu-divider"></div>
                <div className="user-menu-item" onClick={handleLogout}>
                  <i className="fa fa-sign-out-alt"></i>
                  <span>Logout</span>
                </div>
              </div>
            )}
          </div>
        </header>

        {/* Toggle Button */}
        <button
          className="toggle-left-panel"
          onClick={handleToggle}
          ref={toggleButtonRef} // Reference the toggle button
        >
          {showLeftPanel ? (
            <i className="fas fa-times" title="Close Panel"></i> // Exit icon
          ) : (
            <i className="fas fa-bars" title="Open Panel"></i> // Menu icon
          )}
        </button>

        {/* Left Panel */}
        <div
          className={`left-panel ${showLeftPanel ? 'show' : 'hide'}`}
          ref={leftPanelRef} // Reference the left panel
        >
          {/* Logo */}
          <div className="logo">
            <img
              src="/images/veltech_logo(1).png"
              alt="VTU Logo"
              className="logo-img"
            />
          </div>

          {/* New Chat Button */}
          <button className="new-chat-btn" onClick={handleNewChat}>
            <i className="fas fa-plus"></i> New Chat
          </button>

          {/* Recent Chats */}
          <div className="recent-chats-section">
            <div className="recent-chats-header">
              <h3>Recent Chats</h3>
              <div className="sync-controls">
                {isSyncing ? (
                  <span className="sync-indicator" title="Syncing chats...">
                    <i className="fas fa-sync-alt fa-spin"></i>
                  </span>
                ) : (
                  <button
                    className="manual-sync-btn"
                    onClick={manualSync}
                    title="Sync chats across devices"
                  >
                    <i className="fas fa-sync-alt"></i>
                  </button>
                )}
                {syncStatus === 'success' && (
                  <span className="sync-success" title="Sync successful">
                    <i className="fas fa-check-circle"></i>
                  </span>
                )}
                {syncStatus === 'error' && (
                  <span className="sync-error" title="Sync failed">
                    <i className="fas fa-exclamation-circle"></i>
                  </span>
                )}
              </div>
            </div>
            {recentChats.length > 0 ? (
              <div className="recent-chats-list">
                {recentChats.map((chat) => (
                  <div key={chat.id} className={`recent-chat-item ${currentChatId === chat.id ? 'active' : ''}`}>
                    <div className="chat-item-content" onClick={() => loadChat(chat)}>
                      <div className="chat-title">{chat.title}</div>
                      <div className="chat-timestamp" suppressHydrationWarning={true}>
                        {new Date(chat.timestamp).toLocaleDateString()}
                      </div>
                    </div>
                    <button
                      className="delete-chat-btn"
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteChat(chat.id);
                      }}
                      title="Delete chat"
                    >
                      <i className="fas fa-trash"></i>
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-chats">
                <p>No recent chats yet.
                  </p>
              </div>
            )}
          </div>

          {/* Bottom Section - Social Links Only */}
          <div className="left-panel-bottom">
            {/* Social Media Links */}
            <div className="social-media-links">
              <h3>Follow Us</h3>
              <div className="social-icons">
                <a
                  href="https://facebook.com/veltechuniversityofficial"
                  target="_blank"
                  rel="noopener noreferrer"
                  title="Facebook"
                >
                  <i className="fa-brands fa-facebook-f"></i>
                </a>
                <a
                  href="https://twitter.com/veltechofficial"
                  target="_blank"
                  rel="noopener noreferrer"
                  title="Twitter"
                >
                    <i className="fa-brands fa-x-twitter"></i>
                </a>
                <a
                  href="https://www.linkedin.com/in/veltechuniversityofficial/"
                  target="_blank"
                  rel="noopener noreferrer"
                  title="LinkedIn"
                >
                    <i className="fa-brands fa-linkedin-in"></i>
                </a>
                <a
                  href="https://instagram.com/veltechuniversityofficial"
                  target="_blank"
                  rel="noopener noreferrer"
                  title="Instagram"
                >
                  <i className="fa-brands fa-instagram"></i>
                </a>
                <a
                  href="https://www.youtube.com/@veltechofficial"
                  target="_blank"
                  rel="noopener noreferrer"
                  title="YouTube"
                >
                  <i className="fa-brands fa-youtube"></i>
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Chatbot Panel */}
        <div className="chatbot-panel">
          <div className="chat-history">
            {chatHistory.length === 0 ? (
              <div className="welcome-container">
                <div className="welcome-header">
                  <div className="welcome-icon">
                    <i className="fas fa-robot"></i>
                  </div>
                  <h2>How can I help you today?</h2>
                  <p>I'm VTU GPT, your AI assistant for Veltech University. Ask me anything about VTU!</p>
                </div>

                <div className="suggestion-cards">
                  <div className="suggestion-card" onClick={() => handleSendMessage('Tell me about VTU')}>
                    <div className="suggestion-icon">
                      <i className="fas fa-university"></i>
                    </div>
                    <h4>About VTU</h4>
                    <p>Learn about Veltech University's history and mission</p>
                  </div>

                  <div className="suggestion-card" onClick={() => handleSendMessage('What courses are offered at VTU?')}>
                    <div className="suggestion-icon">
                      <i className="fas fa-graduation-cap"></i>
                    </div>
                    <h4>Courses & Programs</h4>
                    <p>Explore available academic programs and courses</p>
                  </div>

                  <div className="suggestion-card" onClick={() => handleSendMessage('VTU admission process')}>
                    <div className="suggestion-icon">
                      <i className="fas fa-file-alt"></i>
                    </div>
                    <h4>Admissions</h4>
                    <p>Get information about admission requirements</p>
                  </div>

                  <div className="suggestion-card" onClick={() => handleSendMessage('VTU campus facilities')}>
                    <div className="suggestion-icon">
                      <i className="fas fa-building"></i>
                    </div>
                    <h4>Campus & Facilities</h4>
                    <p>Discover our campus infrastructure and facilities</p>
                  </div>

                  <div className="suggestion-card" onClick={() => handleSendMessage('VTU achievements and rankings')}>
                    <div className="suggestion-icon">
                      <i className="fas fa-trophy"></i>
                    </div>
                    <h4>Achievements</h4>
                    <p>Learn about VTU's accomplishments and rankings</p>
                  </div>

                  <div className="suggestion-card" onClick={() => handleSendMessage('VTU contact information')}>
                    <div className="suggestion-icon">
                      <i className="fas fa-phone"></i>
                    </div>
                    <h4>Contact Info</h4>
                    <p>Get contact details and location information</p>
                  </div>
                </div>
              </div>
            ) : (
              chatHistory.map((msg, index) => {
                // Ensure message is always a string for rendering
                const displayMessage = typeof msg.message === 'string'
                  ? msg.message
                  : (msg.message ? String(msg.message) : 'Invalid message format');

                return (
                  <div key={index} className={`message ${msg.sender}`}>
                    {displayMessage}
                  </div>
                );
              })
            )}
          </div>

          <div className="input-area">
            {isLoading && <p>Loading...</p>}
            {error && <p className="error">{error}</p>}
            <input
              type="text"
              value={userQuery}
              onChange={handleInputChange}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault(); // Prevents line break in input
                  handleSendMessage();
                }
              }}
              placeholder="Ask a Question"
            />
            <button className="input-area-button" onClick={()=>handleSendMessage()}>
              <i className="fa fa-paper-plane"></i>
            </button>
          </div>
        </div>
      </div>

      {/* Smart Academic Event Calendar */}
      <EventCalendar
        isOpen={showCalendar}
        onClose={() => setShowCalendar(false)}
      />
          </>
        )}
      </div>
    </ClientOnly>
  );
};

export default Chatbot;
